# MMKV 批量搜索工具使用说明

## 概述

这是一个高性能的 MMKV 批量搜索工具，可以在大量 tgz 压缩文件中搜索 MMKV 数据。支持模糊搜索、多关键字搜索、加密文件处理、自定义线程数、临时文件管理等功能。

## 命令行语法

```bash
mmkvreader [OPTIONS] --search <SEARCH_TERMS> <DIRECTORY>
```

## 参数说明

### 必需参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `<DIRECTORY>` | tgz 文件所在目录 | `"E:\data\tgz_files"` |
| `--search <SEARCH_TERMS>` | 搜索关键字 | `"config,camera"` |

### 可选参数

| 参数 | 短参数 | 默认值 | 说明 |
|------|--------|--------|------|
| `--key <CRYPT_KEY>` | `-k` | `fd9f5bef68c54a1ecf70757a6d6f565b` | 加密密钥 |
| `--mode <SEARCH_MODE>` | `-m` | `key` | **搜索模式：`key`（默认）或 `value`** |
| `--zip <COMPRESS>` | `-z` | `yes` | 是否压缩结果：`yes` 或 `no` |
| `--threads <THREADS>` | `-t` | `32` | 线程数（最大1024） |
| `--cleanup <CLEANUP>` | `-c` | `no` | 是否清理临时文件：`yes` 或 `no` |
| `--search-dir <SEARCH_DIR>` | `-d` | 无 | 解压后的搜索目录（如：`mmkv`） |

## 使用示例

### 1. 基本搜索

```bash
# 在指定目录搜索包含 "config" 的 key
mmkvreader "E:\data\tgz_files" --search "config"
```

### 2. 多关键字搜索

```bash
# 支持多种分隔符：中文逗号、英文逗号、空格、竖杠
mmkvreader "E:\data\tgz_files" --search "config,camera|user data"
```

### 3. 在 value 中搜索

```bash
# 在 value 内容中搜索
mmkvreader "E:\data\tgz_files" --search "camera" --mode value
```

### 4. 自定义加密密钥

```bash
# 使用自定义加密密钥
mmkvreader "E:\data\tgz_files" --search "config" --key "your_custom_key"
```

### 5. 不压缩结果

```bash
# 保留单独的 JSON 文件，不压缩
mmkvreader "E:\data\tgz_files" --search "config" --zip no
```

### 6. 自定义线程数

```bash
# 使用8个线程进行并行处理（默认16线程）
mmkvreader "E:\data\tgz_files" --search "config" --threads 8
```

### 7. 指定搜索目录

```bash
# 在解压后的 mmkv 子目录中搜索（优化性能）
mmkvreader "E:\data\tgz_files" --search "config" --search-dir "mmkv"

# 在解压后的 data/mmkv 子目录中搜索
mmkvreader "E:\data\tgz_files" --search "config" --search-dir "data/mmkv"
```

### 8. 清理临时文件

```bash
# 处理完成后自动清理解压目录和JSON文件
mmkvreader "E:\data\tgz_files" --search "config" --cleanup yes
```

### 9. 完整示例

```bash
# 完整的搜索命令
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" \
  --search "config，camera|user data" \
  --mode value \
  --key "fd9f5bef68c54a1ecf70757a6d6f565b" \
  --threads 16 \
  --search-dir "mmkv" \
  --cleanup no \
  --zip yes
```

## 搜索关键字分隔符

支持以下分隔符来分割多个关键字：

| 分隔符 | 说明 | 示例 |
|--------|------|------|
| `,` | 英文逗号 | `"config,camera"` |
| `，` | 中文逗号 | `"config，camera"` |
| ` ` | 空格 | `"config camera"` |
| `|` | 竖杠 | `"config|camera"` |

可以混合使用：`"config，camera|user data"`

## 搜索模式

### 默认搜索模式
- **默认模式**: `key` - 在 MMKV 的 key 名称中搜索
- **可选模式**: `value` - 在 MMKV 的 value 内容中搜索

### 模糊搜索
所有搜索都支持模糊搜索，**不区分大小写**。

**示例：**
- 搜索 `"TOKEN"` 可以匹配 `"token"`、`"Token"`、`"ACCESS_TOKEN"` 等
- 搜索 `"user"` 可以匹配 `"User"`、`"USER_ID"`、`"current_user"` 等

### Key 模式 (`--mode key`)
在 MMKV 的 key 名称中进行模糊搜索。

**示例：**
```bash
mmkvreader "E:\data" --search "USER_ID" --mode key
```
会找到名为 `user_id`、`User_ID_Cache`、`current_user_id` 等的 key。

### Value 模式 (`--mode value`)
在 MMKV 的 value 内容中进行模糊搜索。

**示例：**
```bash
mmkvreader "E:\data" --search "CAMERA" --mode value
```
会找到 value 中包含 "camera"、"Camera"、"CAMERA_SETTINGS" 等字符串的所有 key-value 对。

## 输出结果

### 控制台输出

程序会实时显示处理进度：

```
🔍 MMKV 批量搜索工具
==================================================
📁 目录: E:\data\tgz_files
🔑 加密密钥: fd9f5bef68c54a1ecf70757a6d6f565b
🔍 搜索关键字: ["config", "camera"]
📋 搜索模式: Value
📦 压缩结果: 是

🔍 搜索关键字 1/2: "config"
📦 找到 1500 个 tgz 文件
⏳ 进度: 100/1500 (6.7%), 速度: 12.5 文件/秒, 总匹配: 45
...
✅ 关键字 "config" 搜索完成: 处理 1500 文件，找到 89 匹配

🔍 搜索关键字 2/2: "camera"
...

==================================================
🎉 所有搜索任务完成!
📊 总体统计:
   搜索关键字: 2 个
   总匹配数: 156
   总耗时: 98.45 秒
📦 结果已压缩到: E:\data\tgz_files\mmkv_search_config_camera_20241224_143022.zip
```

### 结果文件

#### 压缩模式 (`--zip yes`)
- 生成一个 ZIP 文件：`mmkv_search_<关键字>_<时间戳>.zip`
- 包含所有匹配的 JSON 结果文件
- 包含搜索摘要文件 `search_summary.txt`
- 自动清理临时 JSON 文件

#### 非压缩模式 (`--zip no`)
- 为每个有匹配的 tgz 文件生成对应的 JSON 文件
- 文件名格式：`<tgz文件名>_search_results.json`

### 文件命名规则

#### 解压目录
- **旧命名**: `example_extracted/`
- **新命名**: `example/` （直接使用tgz文件名）

#### JSON结果文件
- **旧命名**: `example_search_keywords_results.json`
- **新命名**: `example.json` （与tgz文件名相同）
- **内容**: 包含所有搜索关键字的匹配结果

### JSON 结果格式

```json
{
  "tgz_file": "E:\\data\\example.tgz",
  "processed_at": "2024-01-15T10:30:45Z",
  "total_mmkv_files": 3,
  "total_matches": 2,
  "processing_time_ms": 1250,
  "matches": [
    {
      "mmkv_file": "data/user_config",
      "mmkv_id": "user_config",
      "key": "camera_settings",
      "value": "{\"resolution\":\"1920x1080\",\"fps\":30}",
      "value_type": "string",
      "match_type": "value",
      "matched_keywords": ["camera", "settings"]
    }
  ],
  "errors": []
}
```

## 高级功能

### 线程数控制
- **默认线程数**: 32（高性能默认值）
- **最大线程数**: 1024
- **自动限制**: 超过1024会自动限制为1024，0会设置为32
- **性能优化**: 每个线程处理一个tgz文件，避免资源竞争

### 搜索目录优化
- **默认行为**: 在解压根目录中搜索所有文件
- **指定目录**: 使用 `--search-dir` 参数指定子目录（如 `mmkv`）
- **性能提升**: 直接在指定目录搜索，避免递归遍历
- **文件过滤**: 自动跳过 `.crc`、`.log`、`.tmp` 等非MMKV文件

### 临时文件管理
- **解压目录**: 每个tgz文件解压到与文件名相同的目录（不再添加_extracted后缀）
- **JSON结果**: 每个tgz文件生成与文件名相同的JSON结果文件
- **多关键字结果**: 所有搜索关键字的结果合并到同一个JSON文件中
- **清理选项**:
  - `--cleanup no` (默认): 保留解压目录和JSON文件，便于调试
  - `--cleanup yes`: 自动清理解压目录，节省磁盘空间

### ZIP文件命名
- **固定格式**: `ck_search_results_时间戳.zip`
- **时间戳格式**: `YYYYMMDD_HHMMSS`
- **示例**: `ck_search_results_20241224_143022.zip`

## 性能特点

- **超高性能并行处理**: 默认32线程，可设置1-1024个线程
- **智能目录搜索**: 支持指定搜索目录，避免无效遍历
- **文件类型过滤**: 自动跳过 `.crc`、`.log`、`.tmp` 等文件
- **模糊搜索**: 不区分大小写，提高匹配率
- **高效内存**: 流式处理，支持处理 20,000+ 文件
- **实时进度条**: 显示处理进度、速度、匹配数量
- **错误容错**: 单文件失败不影响整体处理
- **简化文件管理**: 解压目录和JSON文件直接使用tgz文件名

## 常见问题

### 1. 目录不存在
```
❌ 错误: 目录不存在: E:\nonexistent
```
**解决**: 检查目录路径是否正确

### 2. 没有找到 tgz 文件
```
❌ 目录中没有找到 tgz 文件
```
**解决**: 确认目录中包含 .tgz 或 .tar.gz 文件

### 3. 加密密钥错误
```
❌ 无法加载 MMKV 文件: 解密失败
```
**解决**: 检查提供的加密密钥是否正确

### 4. 权限问题
```
❌ 创建解压目录失败: 权限被拒绝
```
**解决**: 确保对目标目录有写权限

## 测试命令

使用提供的测试目录：

```bash
# 基本测试（默认按key搜索，忽略大小写）
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "AZEROTH"

# 多关键字模糊搜索测试
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "TOKEN,ADVERTISER_ID,GIFSHOW" --mode value

# 高性能测试（16线程，指定搜索目录）
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "config,camera,user" --mode value --search-dir "mmkv"

# 不压缩测试
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "data" --zip no

# 清理临时文件测试
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "test" --cleanup yes

# 完整功能测试
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" \
  --search "token,advertiser_id,gifshow_,pro_auth_info,devicelD,userlD,passToken,api_st,kwtk,summonArrowConfig,enableShowRocketP" \
  --mode value --threads 8 --cleanup no --zip yes
```

## 版本信息

查看版本：
```bash
mmkvreader --version
```

查看帮助：
```bash
mmkvreader --help
```
