#include "../include/mmkv_c_wrapper.h"
#include "../include/MMKV/MMKV.h"
#include <string>
#include <cstring>
#include <cstdlib>



extern "C" {

int MMKV_initializeMMKV(const char* rootDir) {
    try {
        std::string rootDirStr(rootDir);
        MMKVPath_t path = string2MMKVPath_t(rootDirStr);
        MMKV::initializeMMKV(path);
        return 0;
    } catch (...) {
        return -1;
    }
}

MMKV_Handle MMKV_defaultMMKV(void) {
    try {
        MMKV* mmkv = MMKV::defaultMMKV();
        return static_cast<MMKV_Handle>(mmkv);
    } catch (...) {
        return nullptr;
    }
}

MMKV_Handle MMKV_mmkvWithID(const char* mmapID, int size, int mode, const char* cryptKey, const char* rootPath, int expectedCapacity) {
    if (!mmapID) return nullptr;

    try {
        std::string mmapIDStr(mmapID);

        // Convert mode to MMKVMode enum
        MMKVMode mmkvMode = static_cast<MMKVMode>(mode);

        // Handle optional cryptKey
        const std::string* cryptKeyPtr = nullptr;
        std::string cryptKeyStr;
        if (cryptKey) {
            cryptKeyStr = std::string(cryptKey);
            cryptKeyPtr = &cryptKeyStr;
        }

        // Handle optional rootPath
        const MMKVPath_t* rootPathPtr = nullptr;
        MMKVPath_t rootPathConverted;
        if (rootPath) {
            std::string rootPathStr(rootPath);
            rootPathConverted = string2MMKVPath_t(rootPathStr);
            rootPathPtr = &rootPathConverted;
        }

        // Use the appropriate mmkvWithID function based on platform
        #ifdef MMKV_ANDROID
        MMKV* mmkv = MMKV::mmkvWithID(mmapIDStr, size, mmkvMode, cryptKeyPtr, rootPathPtr, static_cast<size_t>(expectedCapacity));
        #else
        MMKV* mmkv = MMKV::mmkvWithID(mmapIDStr, mmkvMode, cryptKeyPtr, rootPathPtr, static_cast<size_t>(expectedCapacity));
        #endif

        return static_cast<MMKV_Handle>(mmkv);
    } catch (...) {
        return nullptr;
    }
}

int MMKV_setInt32(MMKV_Handle handle, const char* key, int value) {
    if (!handle || !key) return 0;
    
    try {
        MMKV* mmkv = static_cast<MMKV*>(handle);
        std::string keyStr(key);
        bool result = mmkv->set(static_cast<int32_t>(value), keyStr);
        return result ? 1 : 0;
    } catch (...) {
        return 0;
    }
}

int MMKV_getInt32(MMKV_Handle handle, const char* key, int defaultValue) {
    if (!handle || !key) return defaultValue;
    
    try {
        MMKV* mmkv = static_cast<MMKV*>(handle);
        std::string keyStr(key);
        int32_t result = mmkv->getInt32(keyStr, static_cast<int32_t>(defaultValue));
        return static_cast<int>(result);
    } catch (...) {
        return defaultValue;
    }
}

int MMKV_setString(MMKV_Handle handle, const char* key, const char* value) {
    if (!handle || !key || !value) return 0;
    
    try {
        MMKV* mmkv = static_cast<MMKV*>(handle);
        std::string keyStr(key);
        std::string valueStr(value);
        bool result = mmkv->set(valueStr, keyStr);
        return result ? 1 : 0;
    } catch (...) {
        return 0;
    }
}

char* MMKV_getString(MMKV_Handle handle, const char* key) {
    if (!handle || !key) return nullptr;
    
    try {
        MMKV* mmkv = static_cast<MMKV*>(handle);
        std::string keyStr(key);
        std::string result;
        bool found = mmkv->getString(keyStr, result);
        if (found) {
            char* cResult = static_cast<char*>(malloc(result.length() + 1));
            if (cResult) {
                strcpy(cResult, result.c_str());
                return cResult;
            }
        }
        return nullptr;
    } catch (...) {
        return nullptr;
    }
}

void MMKV_freeString(char* str) {
    if (str) {
        free(str);
    }
}

int MMKV_containsKey(MMKV_Handle handle, const char* key) {
    if (!handle || !key) return 0;
    
    try {
        MMKV* mmkv = static_cast<MMKV*>(handle);
        std::string keyStr(key);
        bool result = mmkv->containsKey(keyStr);
        return result ? 1 : 0;
    } catch (...) {
        return 0;
    }
}

int MMKV_removeValueForKey(MMKV_Handle handle, const char* key) {
    if (!handle || !key) return 0;

    try {
        MMKV* mmkv = static_cast<MMKV*>(handle);
        std::string keyStr(key);
        bool result = mmkv->removeValueForKey(keyStr);
        return result ? 1 : 0;
    } catch (...) {
        return 0;
    }
}

char** MMKV_allKeys(MMKV_Handle handle) {
    if (!handle) return nullptr;

    try {
        MMKV* mmkv = static_cast<MMKV*>(handle);
        std::vector<std::string> keys = mmkv->allKeys();

        // Allocate array of char* pointers (including NULL terminator)
        char** result = static_cast<char**>(malloc((keys.size() + 1) * sizeof(char*)));
        if (!result) return nullptr;

        // Copy each key
        for (size_t i = 0; i < keys.size(); ++i) {
            result[i] = static_cast<char*>(malloc(keys[i].length() + 1));
            if (result[i]) {
                strcpy(result[i], keys[i].c_str());
            } else {
                // Cleanup on failure
                for (size_t j = 0; j < i; ++j) {
                    free(result[j]);
                }
                free(result);
                return nullptr;
            }
        }

        // NULL terminator
        result[keys.size()] = nullptr;
        return result;
    } catch (...) {
        return nullptr;
    }
}

void MMKV_freeStringArray(char** array) {
    if (!array) return;

    // Free each string
    for (int i = 0; array[i] != nullptr; ++i) {
        free(array[i]);
    }

    // Free the array itself
    free(array);
}

} // extern "C"
