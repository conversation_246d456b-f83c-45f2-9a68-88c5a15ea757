#ifndef MMKV_C_WRAPPER_H
#define MMKV_C_WRAPPER_H

#ifdef __cplusplus
extern "C" {
#endif

// Forward declaration for the MMKV handle
typedef void* MMKV_Handle;

// Initialize MMKV with a root directory
// Returns 0 on success, non-zero on failure
int MMKV_initializeMMKV(const char* rootDir);

// Get the default MMKV instance
// Returns a handle to the default MMKV instance, or NULL on failure
MMKV_Handle MMKV_defaultMMKV(void);

// Set an int32 value for a key
// Returns 1 on success, 0 on failure
int MMKV_setInt32(MMKV_Handle handle, const char* key, int value);

// Get an int32 value for a key
// Returns the value if found, or defaultValue if not found
int MMKV_getInt32(MMKV_Handle handle, const char* key, int defaultValue);

// Set a string value for a key
// Returns 1 on success, 0 on failure
int MMKV_setString(MMKV_Handle handle, const char* key, const char* value);

// Get a string value for a key
// Returns a newly allocated string that must be freed with MMKV_freeString
// Returns NULL if key not found
char* MMKV_getString(MMKV_Handle handle, const char* key);

// Free a string returned by MMKV_getString
void MMKV_freeString(char* str);

// Check if a key exists
// Returns 1 if key exists, 0 if not
int MMKV_containsKey(MMKV_Handle handle, const char* key);

// Remove a key
// Returns 1 on success, 0 on failure
int MMKV_removeValueForKey(MMKV_Handle handle, const char* key);

// Get all keys
// Returns an array of strings terminated by NULL
// Each string and the array itself must be freed with MMKV_freeStringArray
char** MMKV_allKeys(MMKV_Handle handle);

// Free a string array returned by MMKV_allKeys
void MMKV_freeStringArray(char** array);

#ifdef __cplusplus
}
#endif

#endif // MMKV_C_WRAPPER_H
