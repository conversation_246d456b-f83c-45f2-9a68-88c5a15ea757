
// extern crate bindgen;

use std::env;
use std::path::PathBuf;

fn main() {
    // 告诉 cargo 链接 mmkv.lib
    println!("cargo:rustc-link-search=lib");
    println!("cargo:rustc-link-lib=mmkv");

    // 设置 bindgen
    let bindings = bindgen::Builder::default()
        .header("include/MMKV.h")
        .parse_callbacks(Box::new(bindgen::CargoCallbacks))
        .generate()
        .expect("Unable to generate bindings");

    // 写入输出文件
    let out_path = PathBuf::from(env::var("OUT_DIR").unwrap());
    bindings
        .write_to_file(out_path.join("bindings.rs"))
        .expect("Couldn't write bindings!");
}