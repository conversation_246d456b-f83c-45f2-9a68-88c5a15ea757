// src/lib.rs

// Include the generated bindings
include!(concat!(env!("OUT_DIR"), "/bindings.rs"));
use std::ffi::CString;
use std::path::Path;
use std::fs;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use std::time::Instant;

use anyhow::{Result, Context};
use flate2::read::GzDecoder;
use rayon::prelude::*;
use serde::{Deserialize, Serialize};
use tar::Archive;
use walkdir::WalkDir;
use indicatif::{ProgressBar, ProgressStyle};

// MMKV Mode constants
pub const MMKV_SINGLE_PROCESS: i32 = 1;
pub const MMKV_MULTI_PROCESS: i32 = 2;

// 搜索模式枚举
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum SearchMode {
    Key = 1,
    Value = 2,
}

impl From<i32> for SearchMode {
    fn from(value: i32) -> Self {
        match value {
            1 => SearchMode::Key,
            2 => SearchMode::Value,
            _ => SearchMode::Key, // 默认搜索 key
        }
    }
}

// 搜索结果结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub mmkv_file: String,
    pub mmkv_id: String,
    pub key: String,
    pub value: String,
    pub value_type: String,
    pub match_type: String, // "key" 或 "value"
    pub matched_keywords: Vec<String>, // 匹配的关键字列表
}

// 文件处理结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileProcessResult {
    pub tgz_file: String,
    pub processed_at: String,
    pub total_mmkv_files: usize,
    pub total_matches: usize,
    pub processing_time_ms: u64,
    pub matches: Vec<SearchResult>,
    pub errors: Vec<String>,
}

// 处理统计信息
#[derive(Debug, Clone)]
pub struct ProcessingStats {
    pub total_files: usize,
    pub processed_files: usize,
    pub failed_files: usize,
    pub total_matches: usize,
    pub start_time: Instant,
}

// 数据类型枚举
#[derive(Debug, Clone)]
pub enum MmkvValue {
    Integer(i32),
    String(String),
    Unknown,
}

impl std::fmt::Display for MmkvValue {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MmkvValue::Integer(i) => write!(f, "{} (int32)", i),
            MmkvValue::String(s) => write!(f, "\"{}\" (string)", s),
            MmkvValue::Unknown => write!(f, "<unknown type>"),
        }
    }
}

pub struct Mmkv {
    handle: MMKV_Handle,
}

impl Mmkv {
    pub fn initialize(path: &Path) -> Result<(), i32> {
        let path_str = path.to_str().unwrap();
        let c_path = CString::new(path_str).unwrap();
        let ret = unsafe { MMKV_initializeMMKV(c_path.as_ptr()) };
        if ret == 0 {
            Ok(())
        } else {
            Err(ret)
        }
    }
    
    pub fn default() -> Self {
        let handle = unsafe { MMKV_defaultMMKV() };
        Mmkv { handle }
    }

    pub fn with_id(mmap_id: &str, size: i32, mode: i32, crypt_key: Option<&str>, root_path: Option<&str>, expected_capacity: i32) -> Option<Self> {
        let c_mmap_id = CString::new(mmap_id).ok()?;

        let c_crypt_key = if let Some(key) = crypt_key {
            Some(CString::new(key).ok()?)
        } else {
            None
        };

        let c_root_path = if let Some(path) = root_path {
            Some(CString::new(path).ok()?)
        } else {
            None
        };

        let handle = unsafe {
            MMKV_mmkvWithID(
                c_mmap_id.as_ptr(),
                size,
                mode,
                c_crypt_key.as_ref().map_or(std::ptr::null(), |s| s.as_ptr()),
                c_root_path.as_ref().map_or(std::ptr::null(), |s| s.as_ptr()),
                expected_capacity
            )
        };

        if handle.is_null() {
            None
        } else {
            Some(Mmkv { handle })
        }
    }

    pub fn set_i32(&self, key: &str, value: i32) -> bool {
        let c_key = CString::new(key).unwrap();
        let result = unsafe { MMKV_setInt32(self.handle, c_key.as_ptr(), value) };
        result != 0
    }

    pub fn get_i32(&self, key: &str, default: i32) -> i32 {
        let c_key = CString::new(key).unwrap();
        unsafe { MMKV_getInt32(self.handle, c_key.as_ptr(), default) }
    }

    pub fn set_string(&self, key: &str, value: &str) -> bool {
        let c_key = CString::new(key).unwrap();
        let c_value = CString::new(value).unwrap();
        let result = unsafe { MMKV_setString(self.handle, c_key.as_ptr(), c_value.as_ptr()) };
        result != 0
    }

    pub fn get_string(&self, key: &str) -> Option<String> {
        let c_key = CString::new(key).unwrap();
        unsafe {
            let c_str_ptr = MMKV_getString(self.handle, c_key.as_ptr());
            if c_str_ptr.is_null() {
                None
            } else {
                let c_str = std::ffi::CStr::from_ptr(c_str_ptr);
                let result = c_str.to_str().ok().map(|s| s.to_string());
                MMKV_freeString(c_str_ptr);
                result
            }
        }
    }

    pub fn contains_key(&self, key: &str) -> bool {
        let c_key = CString::new(key).unwrap();
        let result = unsafe { MMKV_containsKey(self.handle, c_key.as_ptr()) };
        result != 0
    }

    pub fn all_keys(&self) -> Vec<String> {
        unsafe {
            let keys_ptr = MMKV_allKeys(self.handle);
            if keys_ptr.is_null() {
                return Vec::new();
            }

            let mut keys = Vec::new();
            let mut i = 0;

            // Iterate through the NULL-terminated array
            while !(*keys_ptr.add(i)).is_null() {
                let c_str = std::ffi::CStr::from_ptr(*keys_ptr.add(i));
                if let Ok(key_str) = c_str.to_str() {
                    keys.push(key_str.to_string());
                }
                i += 1;
            }

            // Free the array
            MMKV_freeStringArray(keys_ptr);
            keys
        }
    }

    /// 尝试读取 key 的值，自动检测数据类型
    pub fn get_value(&self, key: &str) -> MmkvValue {
        // 首先尝试读取为字符串
        if let Some(string_value) = self.get_string(key) {
            return MmkvValue::String(string_value);
        }

        // 如果字符串读取失败，尝试读取为整数
        // 使用一个特殊的默认值来检测是否真的存在这个 key
        let special_default = i32::MIN;
        let int_value = self.get_i32(key, special_default);

        if int_value != special_default || self.contains_key(key) {
            return MmkvValue::Integer(int_value);
        }

        MmkvValue::Unknown
    }

    /// 输出当前实例的所有 keys 和对应的值
    pub fn print_all_keys_and_values(&self, instance_name: &str) {
        println!("=== {} MMKV 实例的所有 keys 和值 ===", instance_name);

        let all_keys = self.all_keys();

        if all_keys.is_empty() {
            println!("  没有找到任何 keys");
            return;
        }

        println!("总共找到 {} 个 keys:\n", all_keys.len());

        for (index, key) in all_keys.iter().enumerate() {
            let value = self.get_value(key);
            println!("  {}: {} = {}", index + 1, key, value);
        }

        println!();
    }

    /// 静态方法：输出指定 mmapID 的所有 keys 和对应的值
    pub fn print_mmkv_keys_and_values(
        mmap_id: &str,
        crypt_key: Option<&str>,
        root_path: Option<&str>,
        mode: Option<i32>,
    ) -> Result<(), String> {
        println!("=== 读取 {} MMKV 实例 ===", mmap_id);

        if let Some(key) = crypt_key {
            println!("使用加密密钥: {}", key);
        } else {
            println!("无加密");
        }

        if let Some(path) = root_path {
            println!("根目录: {}", path);
        }

        let mmkv = Self::with_id(
            mmap_id,
            0,  // size
            mode.unwrap_or(MMKV_SINGLE_PROCESS),  // mode
            crypt_key,
            root_path,
            0,  // expected_capacity
        );

        match mmkv {
            Some(instance) => {
                println!("✅ 成功加载 {} MMKV 实例\n", mmap_id);
                instance.print_all_keys_and_values(mmap_id);
                Ok(())
            }
            None => {
                let error_msg = format!("❌ 无法加载 {} MMKV 实例", mmap_id);
                println!("{}", error_msg);
                println!("可能的原因:");
                println!("  1. 指定的 mmapID '{}' 不存在", mmap_id);
                if crypt_key.is_some() {
                    println!("  2. 加密密钥不正确");
                }
                println!("  3. 文件权限问题");
                println!("  4. 文件损坏");
                println!("  5. 根目录路径不正确");
                Err(error_msg)
            }
        }
    }

    /// 在当前 MMKV 实例中搜索多个关键字
    pub fn search_multiple_keywords(&self, search_terms: &[String], search_mode: SearchMode, mmkv_file: &str, mmkv_id: &str) -> Vec<SearchResult> {
        let mut results = Vec::new();
        let all_keys = self.all_keys();

        for key in all_keys {
            let mut matched_keywords = Vec::new();
            let mut match_type = String::new();

            // 获取 value 用于搜索
            let value = self.get_value(&key);
            let value_str = match &value {
                MmkvValue::String(s) => s.clone(),
                MmkvValue::Integer(i) => i.to_string(),
                MmkvValue::Unknown => "unknown".to_string(),
            };

            // 检查每个关键字 - 使用模糊搜索
            for search_term in search_terms {
                let mut keyword_matched = false;

                match search_mode {
                    SearchMode::Key => {
                        // 模糊搜索：不区分大小写
                        if key.to_lowercase().contains(&search_term.to_lowercase()) {
                            keyword_matched = true;
                            match_type = "key".to_string();
                        }
                    }
                    SearchMode::Value => {
                        // 模糊搜索：不区分大小写
                        if value_str.to_lowercase().contains(&search_term.to_lowercase()) {
                            keyword_matched = true;
                            match_type = "value".to_string();
                        }
                    }
                }

                if keyword_matched {
                    matched_keywords.push(search_term.clone());
                }
            }

            // 如果有任何关键字匹配，添加到结果中
            if !matched_keywords.is_empty() {
                let (final_value_str, value_type) = match value {
                    MmkvValue::String(s) => (s, "string".to_string()),
                    MmkvValue::Integer(i) => (i.to_string(), "int32".to_string()),
                    MmkvValue::Unknown => ("unknown".to_string(), "unknown".to_string()),
                };

                results.push(SearchResult {
                    mmkv_file: mmkv_file.to_string(),
                    mmkv_id: mmkv_id.to_string(),
                    key: key.clone(),
                    value: final_value_str,
                    value_type,
                    match_type: match_type.clone(),
                    matched_keywords,
                });
            }
        }

        results
    }

    /// 在当前 MMKV 实例中搜索单个关键字（兼容性方法）
    pub fn search_content(&self, search_term: &str, search_mode: SearchMode, mmkv_file: &str, mmkv_id: &str) -> Vec<SearchResult> {
        // 调用多关键字搜索方法，传入单个关键字
        self.search_multiple_keywords(&[search_term.to_string()], search_mode, mmkv_file, mmkv_id)
    }

    /// 高性能搜索方法：处理目录下的所有 tgz 文件，支持多关键字搜索
    pub fn search_in_tgz_directory_multi_keywords(
        directory: &str,
        crypt_key: Option<&str>,
        search_terms: &[String],
        search_mode: i32,
        threads: usize,
        should_cleanup: bool,
        search_dir: Option<&str>,
    ) -> Result<ProcessingStats> {
        let search_mode = SearchMode::from(search_mode);
        let directory_path = Path::new(directory);

        if !directory_path.exists() {
            return Err(anyhow::anyhow!("目录不存在: {}", directory));
        }

        println!("🔍 开始多关键字搜索任务");
        println!("📁 目录: {}", directory);
        println!("🔑 加密密钥: {}", crypt_key.unwrap_or("无"));
        println!("🔍 搜索关键字: {:?}", search_terms);
        println!("📋 搜索模式: {:?}", search_mode);
        println!("🧵 线程数: {}", threads);
        println!("🧹 清理临时文件: {}", if should_cleanup { "是" } else { "否" });
        if let Some(search_dir) = search_dir {
            println!("📂 搜索目录: {}", search_dir);
        }

        // 收集所有 tgz 文件
        let tgz_files: Vec<PathBuf> = WalkDir::new(directory_path)
            .into_iter()
            .filter_map(|entry| entry.ok())
            .filter(|entry| {
                entry.path().extension()
                    .and_then(|ext| ext.to_str())
                    .map(|ext| ext.eq_ignore_ascii_case("tgz") || ext.eq_ignore_ascii_case("tar.gz"))
                    .unwrap_or(false)
            })
            .map(|entry| entry.path().to_path_buf())
            .collect();

        let total_files = tgz_files.len();
        println!("📦 找到 {} 个 tgz 文件", total_files);

        if total_files == 0 {
            return Err(anyhow::anyhow!("目录中没有找到 tgz 文件"));
        }

        // 创建进度条
        let progress_bar = ProgressBar::new(total_files as u64);
        progress_bar.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta}) {msg}")
                .unwrap()
                .progress_chars("#>-")
        );
        progress_bar.set_message("处理 tgz 文件...");

        let stats = Arc::new(Mutex::new(ProcessingStats {
            total_files,
            processed_files: 0,
            failed_files: 0,
            total_matches: 0,
            start_time: Instant::now(),
        }));

        // 设置线程池
        let thread_pool = rayon::ThreadPoolBuilder::new()
            .num_threads(threads)
            .build()
            .context("创建线程池失败")?;

        // 使用自定义线程池进行并行处理
        let progress_bar_clone = progress_bar.clone();

        thread_pool.install(|| {
            tgz_files
                .par_iter()
                .for_each(|tgz_file| {
                    let result = Self::process_single_tgz_file_multi_keywords(
                        tgz_file,
                        crypt_key,
                        search_terms,
                        search_mode,
                        should_cleanup,
                        search_dir,
                    );

                    let mut stats_guard = stats.lock().unwrap();
                    match result {
                        Ok(file_result) => {
                            stats_guard.processed_files += 1;
                            stats_guard.total_matches += file_result.total_matches;

                            // 更新进度条
                            progress_bar_clone.inc(1);
                            progress_bar_clone.set_message(format!(
                                "已处理: {} | 匹配: {} | 速度: {:.1} 文件/秒",
                                stats_guard.processed_files,
                                stats_guard.total_matches,
                                stats_guard.processed_files as f64 / stats_guard.start_time.elapsed().as_secs_f64()
                            ));
                        }
                        Err(e) => {
                            stats_guard.failed_files += 1;
                            progress_bar_clone.inc(1);
                            progress_bar_clone.println(format!("❌ 处理文件失败 {}: {}", tgz_file.display(), e));
                        }
                    }
                })
        });

        progress_bar.finish_with_message("搜索完成!");

        let final_stats = stats.lock().unwrap().clone();
        let elapsed = final_stats.start_time.elapsed();

        println!("\n✅ 多关键字搜索完成!");
        println!("📊 统计信息:");
        println!("   总文件数: {}", final_stats.total_files);
        println!("   成功处理: {}", final_stats.processed_files);
        println!("   失败文件: {}", final_stats.failed_files);
        println!("   总匹配数: {}", final_stats.total_matches);
        println!("   总耗时: {:.2} 秒", elapsed.as_secs_f64());
        println!("   平均速度: {:.1} 文件/秒", final_stats.processed_files as f64 / elapsed.as_secs_f64());

        Ok(final_stats)
    }

    /// 处理单个 tgz 文件
    fn process_single_tgz_file(
        tgz_path: &Path,
        crypt_key: Option<&str>,
        search_term: &str,
        search_mode: SearchMode,
    ) -> Result<FileProcessResult> {
        let start_time = Instant::now();
        let tgz_name = tgz_path.file_stem()
            .and_then(|name| name.to_str())
            .unwrap_or("unknown");

        // 创建解压目录
        let extract_dir = tgz_path.parent()
            .unwrap_or_else(|| Path::new("."))
            .join(format!("{}_extracted", tgz_name));

        // 如果目录已存在，先删除
        if extract_dir.exists() {
            fs::remove_dir_all(&extract_dir)
                .context("删除已存在的解压目录失败")?;
        }

        fs::create_dir_all(&extract_dir)
            .context("创建解压目录失败")?;

        // 解压 tgz 文件
        Self::extract_tgz(tgz_path, &extract_dir)
            .context("解压 tgz 文件失败")?;

        // 搜索 MMKV 文件
        let mut matches = Vec::new();
        let mut errors = Vec::new();
        let mut mmkv_file_count = 0;

        // 查找所有可能的 MMKV 文件
        for entry in WalkDir::new(&extract_dir) {
            let entry = match entry {
                Ok(e) => e,
                Err(e) => {
                    errors.push(format!("遍历目录错误: {}", e));
                    continue;
                }
            };

            let path = entry.path();
            if path.is_file() {
                // 尝试作为 MMKV 文件处理
                if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                    // 跳过 .crc 文件
                    if file_name.ends_with(".crc") {
                        continue;
                    }

                    // 尝试加载为 MMKV
                    match Self::search_in_mmkv_file(path, file_name, crypt_key, search_term, search_mode) {
                        Ok(mut file_matches) => {
                            mmkv_file_count += 1;
                            matches.append(&mut file_matches);
                        }
                        Err(_) => {
                            // 不是所有文件都是 MMKV 文件，所以这里不记录错误
                            // errors.push(format!("处理文件 {} 失败: {}", path.display(), e));
                        }
                    }
                }
            }
        }

        let processing_time = start_time.elapsed().as_millis() as u64;

        let result = FileProcessResult {
            tgz_file: tgz_path.display().to_string(),
            processed_at: chrono::Utc::now().to_rfc3339(),
            total_mmkv_files: mmkv_file_count,
            total_matches: matches.len(),
            processing_time_ms: processing_time,
            matches,
            errors,
        };

        // 保存结果到 JSON 文件
        let json_path = tgz_path.parent()
            .unwrap_or_else(|| Path::new("."))
            .join(format!("{}_search_results.json", tgz_name));

        let json_content = serde_json::to_string_pretty(&result)
            .context("序列化搜索结果失败")?;

        fs::write(&json_path, json_content)
            .context("写入 JSON 结果文件失败")?;

        // 清理解压目录
        if extract_dir.exists() {
            let _ = fs::remove_dir_all(&extract_dir); // 忽略清理错误
        }

        Ok(result)
    }

    /// 处理单个 tgz 文件，支持多关键字搜索
    fn process_single_tgz_file_multi_keywords(
        tgz_path: &Path,
        crypt_key: Option<&str>,
        search_terms: &[String],
        search_mode: SearchMode,
        should_cleanup: bool,
        search_dir: Option<&str>,
    ) -> Result<FileProcessResult> {
        let start_time = Instant::now();
        let tgz_name = tgz_path.file_stem()
            .and_then(|name| name.to_str())
            .unwrap_or("unknown");

        // 创建解压目录
        let extract_dir = tgz_path.parent()
            .unwrap_or_else(|| Path::new("."))
            .join(format!("{}_extracted", tgz_name));

        // 如果目录已存在，先删除
        if extract_dir.exists() {
            fs::remove_dir_all(&extract_dir)
                .context("删除已存在的解压目录失败")?;
        }

        fs::create_dir_all(&extract_dir)
            .context("创建解压目录失败")?;

        // 解压 tgz 文件
        Self::extract_tgz(tgz_path, &extract_dir)
            .context("解压 tgz 文件失败")?;

        // 确定搜索目录
        let search_path = if let Some(search_dir) = search_dir {
            extract_dir.join(search_dir)
        } else {
            extract_dir.clone()
        };

        // 搜索 MMKV 文件 - 优化：直接在指定目录中查找，不递归搜索
        let mut matches = Vec::new();
        let mut errors = Vec::new();
        let mut mmkv_file_count = 0;

        if search_path.exists() && search_path.is_dir() {
            // 直接读取目录中的文件，不递归搜索
            match fs::read_dir(&search_path) {
                Ok(entries) => {
                    for entry in entries {
                        let entry = match entry {
                            Ok(e) => e,
                            Err(e) => {
                                errors.push(format!("读取目录项错误: {}", e));
                                continue;
                            }
                        };

                        let path = entry.path();
                        if path.is_file() {
                            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                                // 优化：跳过 .crc 文件和其他明显不是 MMKV 的文件
                                if file_name.ends_with(".crc") ||
                                   file_name.ends_with(".log") ||
                                   file_name.ends_with(".tmp") {
                                    continue;
                                }

                                // 尝试加载为 MMKV 并搜索多个关键字
                                match Self::search_in_mmkv_file_multi_keywords(&path, file_name, crypt_key, search_terms, search_mode) {
                                    Ok(mut file_matches) => {
                                        mmkv_file_count += 1;
                                        matches.append(&mut file_matches);
                                    }
                                    Err(_) => {
                                        // 不是所有文件都是 MMKV 文件，所以这里不记录错误
                                    }
                                }
                            }
                        }
                    }
                }
                Err(e) => {
                    errors.push(format!("无法读取搜索目录 {}: {}", search_path.display(), e));
                }
            }
        } else {
            errors.push(format!("搜索目录不存在: {}", search_path.display()));
        }

        let processing_time = start_time.elapsed().as_millis() as u64;

        let result = FileProcessResult {
            tgz_file: tgz_path.display().to_string(),
            processed_at: chrono::Utc::now().to_rfc3339(),
            total_mmkv_files: mmkv_file_count,
            total_matches: matches.len(),
            processing_time_ms: processing_time,
            matches,
            errors,
        };

        // 保存结果到 JSON 文件
        let keywords_str = search_terms.join("_");
        let safe_keywords = keywords_str.chars()
            .map(|c| if c.is_alphanumeric() || c == '_' { c } else { '_' })
            .collect::<String>();

        let json_path = tgz_path.parent()
            .unwrap_or_else(|| Path::new("."))
            .join(format!("{}_search_{}_results.json", tgz_name, safe_keywords));

        let json_content = serde_json::to_string_pretty(&result)
            .context("序列化搜索结果失败")?;

        fs::write(&json_path, json_content)
            .context("写入 JSON 结果文件失败")?;

        // 根据参数决定是否清理解压目录
        if should_cleanup && extract_dir.exists() {
            let _ = fs::remove_dir_all(&extract_dir); // 忽略清理错误
        }

        Ok(result)
    }

    /// 解压 tgz 文件
    fn extract_tgz(tgz_path: &Path, extract_dir: &Path) -> Result<()> {
        let file = fs::File::open(tgz_path)
            .context("打开 tgz 文件失败")?;

        let decoder = GzDecoder::new(file);
        let mut archive = Archive::new(decoder);

        archive.unpack(extract_dir)
            .context("解压 tar 归档失败")?;

        Ok(())
    }

    /// 在单个 MMKV 文件中搜索
    fn search_in_mmkv_file(
        file_path: &Path,
        file_name: &str,
        crypt_key: Option<&str>,
        search_term: &str,
        search_mode: SearchMode,
    ) -> Result<Vec<SearchResult>> {
        // 尝试不同的方式加载 MMKV 文件

        // 首先尝试直接使用文件名作为 mmapID
        if let Some(mmkv) = Self::with_id(
            file_name,
            0,
            MMKV_SINGLE_PROCESS,
            crypt_key,
            file_path.parent().and_then(|p| p.to_str()),
            0,
        ) {
            let results = mmkv.search_content(search_term, search_mode, &file_path.display().to_string(), file_name);
            if !results.is_empty() {
                return Ok(results);
            }
        }

        // 如果失败，尝试去掉扩展名
        if let Some(stem) = file_path.file_stem().and_then(|s| s.to_str()) {
            if let Some(mmkv) = Self::with_id(
                stem,
                0,
                MMKV_SINGLE_PROCESS,
                crypt_key,
                file_path.parent().and_then(|p| p.to_str()),
                0,
            ) {
                let results = mmkv.search_content(search_term, search_mode, &file_path.display().to_string(), stem);
                if !results.is_empty() {
                    return Ok(results);
                }
            }
        }

        // 如果还是失败，返回错误
        Err(anyhow::anyhow!("无法加载 MMKV 文件: {}", file_path.display()))
    }

    /// 在单个 MMKV 文件中搜索多个关键字
    fn search_in_mmkv_file_multi_keywords(
        file_path: &Path,
        file_name: &str,
        crypt_key: Option<&str>,
        search_terms: &[String],
        search_mode: SearchMode,
    ) -> Result<Vec<SearchResult>> {
        // 尝试不同的方式加载 MMKV 文件

        // 首先尝试直接使用文件名作为 mmapID
        if let Some(mmkv) = Self::with_id(
            file_name,
            0,
            MMKV_SINGLE_PROCESS,
            crypt_key,
            file_path.parent().and_then(|p| p.to_str()),
            0,
        ) {
            let results = mmkv.search_multiple_keywords(search_terms, search_mode, &file_path.display().to_string(), file_name);
            if !results.is_empty() {
                return Ok(results);
            }
        }

        // 如果失败，尝试去掉扩展名
        if let Some(stem) = file_path.file_stem().and_then(|s| s.to_str()) {
            if let Some(mmkv) = Self::with_id(
                stem,
                0,
                MMKV_SINGLE_PROCESS,
                crypt_key,
                file_path.parent().and_then(|p| p.to_str()),
                0,
            ) {
                let results = mmkv.search_multiple_keywords(search_terms, search_mode, &file_path.display().to_string(), stem);
                if !results.is_empty() {
                    return Ok(results);
                }
            }
        }

        // 如果还是失败，返回错误
        Err(anyhow::anyhow!("无法加载 MMKV 文件: {}", file_path.display()))
    }
}

impl Drop for Mmkv {
    fn drop(&mut self) {
        // 如果有释放函数，应该在这里调用
    }
}