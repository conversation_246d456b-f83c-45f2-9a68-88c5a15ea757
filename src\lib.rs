// src/lib.rs
// mod ffi;
use ffi::*;
use std::ffi::CString;
use std::path::Path;

pub struct Mmkv {
    ptr: *mut MMKV,
}

impl Mmkv {
    pub fn initialize(path: &Path) -> Result<(), i32> {
        let path_str = path.to_str().unwrap();
        let c_path = CString::new(path_str).unwrap();
        let ret = unsafe { MMKV_initializeMMKV(c_path.as_ptr()) };
        if ret == 0 {
            Ok(())
        } else {
            Err(ret)
        }
    }
    
    pub fn default() -> Self {
        let ptr = unsafe { MMKV_defaultMMKV() };
        Mmkv { ptr }
    }
    
    pub fn set_i32(&self, key: &str, value: i32) -> bool {
        let c_key = CString::new(key).unwrap();
        unsafe { MMKV_setInt32(self.ptr, c_key.as_ptr(), value) }
    }
    
    pub fn get_i32(&self, key: &str, default: i32) -> i32 {
        let c_key = CString::new(key).unwrap();
        unsafe { MMKV_getInt32(self.ptr, c_key.as_ptr(), default) }
    }
}

impl Drop for Mmkv {
    fn drop(&mut self) {
        // 如果有释放函数，应该在这里调用
    }
}