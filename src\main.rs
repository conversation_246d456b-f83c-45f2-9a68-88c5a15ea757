// src/main.rs
use mmkvreader::{Mmkv, MMKV_SINGLE_PROCESS, SearchMode};
use std::path::Path;
use std::env;

fn main() {
    let args: Vec<String> = env::args().collect();

    if args.len() > 1 && args[1] == "search" {
        // 搜索模式
        run_search_demo();
    } else {
        // 默认模式：读取单个 MMKV 实例
        run_single_mmkv_demo();
    }
}

fn run_search_demo() {
    println!("🔍 MMKV 批量搜索演示");
    println!("{}", "=".repeat(50));

    // 搜索参数 - 你可以根据需要修改这些参数
    let directory = "E:\\test_tgz_files";  // 包含 tgz 文件的目录
    let crypt_key = Some("fd9f5bef68c54a1ecf70757a6d6f565b");  // 加密密钥
    let search_term = "config";  // 搜索内容
    let search_mode = 2;  // 1=搜索key, 2=搜索value

    println!("📋 搜索参数:");
    println!("   目录: {}", directory);
    println!("   加密密钥: {}", crypt_key.unwrap_or("无"));
    println!("   搜索内容: {}", search_term);
    println!("   搜索模式: {} ({})", search_mode,
        if search_mode == 1 { "搜索 Key" } else { "搜索 Value" });
    println!();

    // 检查目录是否存在
    if !Path::new(directory).exists() {
        println!("❌ 目录不存在: {}", directory);
        println!("💡 请创建测试目录并放入一些 tgz 文件，或修改 main.rs 中的目录路径");
        return;
    }

    // 执行搜索
    match Mmkv::search_in_tgz_directory(directory, crypt_key, search_term, search_mode) {
        Ok(stats) => {
            println!("🎉 搜索任务完成!");
            println!("📊 最终统计:");
            println!("   处理文件: {}/{}", stats.processed_files, stats.total_files);
            println!("   失败文件: {}", stats.failed_files);
            println!("   总匹配数: {}", stats.total_matches);
            println!("   总耗时: {:.2} 秒", stats.start_time.elapsed().as_secs_f64());
        }
        Err(e) => {
            println!("❌ 搜索失败: {}", e);
        }
    }
}

fn run_single_mmkv_demo() {
    println!("📖 MMKV 单文件读取演示");
    println!("{}", "=".repeat(50));

    // 配置参数
    let root_path = "E:\\360MoveData\\Users\\LiShixi\\Desktop\\CK20240714\\mmkv";
    let mmap_id = "azeroth";
    let crypt_key = "fd9f5bef68c54a1ecf70757a6d6f565b";

    // 初始化 MMKV
    println!("初始化 MMKV，根目录: {}", root_path);
    Mmkv::initialize(Path::new(root_path)).unwrap();

    // 使用新的静态方法来输出指定 mmapID 的所有 keys 和值
    match Mmkv::print_mmkv_keys_and_values(
        mmap_id,
        Some(crypt_key),
        Some(root_path),
        Some(MMKV_SINGLE_PROCESS),
    ) {
        Ok(()) => println!("✅ 成功读取并输出了所有数据"),
        Err(e) => println!("❌ 读取失败: {}", e),
    }

    println!("\n💡 提示: 运行 'cargo run search' 来演示批量搜索功能");
}