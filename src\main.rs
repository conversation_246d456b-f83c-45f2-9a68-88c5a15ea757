// src/main.rs
use mmkvreader::{Mmkv, MMKV_SINGLE_PROCESS};
use std::path::Path;

fn main() {
    // 初始化
    Mmkv::initialize(Path::new("E:\\360MoveData\\Users\\LiShixi\\Desktop\\CK20240714\\mmkv")).unwrap();

    // 读取指定的 azeroth MMKV 实例
    println!("=== 读取 azeroth MMKV 实例的所有 keys ===");
    let azeroth_mmkv = Mmkv::with_id(
        "azeroth",                           // mmapID
        0,                                   // size (使用默认)
        MMKV_SINGLE_PROCESS,                 // mode
        Some("fd9f5bef68c54a1ecf70757a6d6f565b"), // crypt_key
        None,                                // rootPath (使用默认)
        0                                    // expectedCapacity (使用默认)
    );

    match azeroth_mmkv {
        Some(mmkv) => {
            println!("成功加载 azeroth MMKV 实例");

            // 获取所有 keys
            let all_keys = mmkv.all_keys();

            if all_keys.is_empty() {
                println!("azeroth MMKV 实例中没有找到任何 keys");
            } else {
                println!("azeroth MMKV 实例中的所有 keys:");
                for (index, key) in all_keys.iter().enumerate() {
                    println!("  {}: {}", index + 1, key);

                    // 尝试读取每个 key 的值（假设都是 int32 类型）
                    let value = mmkv.get_i32(key, -1);
                    println!("     值: {}", value);
                }
                println!("\n总共找到 {} 个 keys", all_keys.len());
            }
        }
        None => {
            println!("无法加载 azeroth MMKV 实例，可能的原因：");
            println!("  1. 指定的 mmapID 'azeroth' 不存在");
            println!("  2. 加密密钥不正确");
            println!("  3. 文件权限问题");
            println!("  4. 文件损坏");
        }
    }

    return; // 提前返回，跳过下面的演示代码
    
    // 使用默认 MMKV 实例
    let mmkv = Mmkv::default();

    // 存储一些测试数据
    mmkv.set_i32("test_key1", 42);
    mmkv.set_i32("test_key2", 100);
    mmkv.set_i32("number", 999);

    // 读取数据
    let value1 = mmkv.get_i32("test_key1", 0);
    let value2 = mmkv.get_i32("test_key2", 0);
    let value3 = mmkv.get_i32("number", 0);

    println!("test_key1 value: {}", value1);
    println!("test_key2 value: {}", value2);
    println!("number value: {}", value3);

    // 获取所有keys并输出
    let all_keys = mmkv.all_keys();
    println!("\nAll keys in MMKV:");
    for (index, key) in all_keys.iter().enumerate() {
        println!("  {}: {}", index + 1, key);
    }

    if all_keys.is_empty() {
        println!("  No keys found in MMKV");
    } else {
        println!("\nTotal keys: {}", all_keys.len());
    }

    // 演示使用指定的 mmapID 创建 MMKV 实例
    println!("\n=== 使用指定 mmapID 创建 MMKV 实例 ===");

    // 创建一个名为 "user_data" 的 MMKV 实例
    let user_mmkv = Mmkv::with_id(
        "user_data",           // mmapID
        0,                     // size (0 = 使用默认大小)
        MMKV_SINGLE_PROCESS,   // mode (单进程模式)
        None,                  // cryptKey (无加密)
        None,                  // rootPath (使用默认路径)
        0                      // expectedCapacity (0 = 使用默认容量)
    );

    if let Some(user_mmkv) = user_mmkv {
        println!("成功创建 user_data MMKV 实例");

        // 在这个实例中存储一些用户数据
        user_mmkv.set_i32("user_id", 12345);
        user_mmkv.set_i32("user_level", 10);
        user_mmkv.set_i32("user_score", 9999);

        // 读取数据
        let user_id = user_mmkv.get_i32("user_id", 0);
        let user_level = user_mmkv.get_i32("user_level", 1);
        let user_score = user_mmkv.get_i32("user_score", 0);

        println!("用户ID: {}", user_id);
        println!("用户等级: {}", user_level);
        println!("用户分数: {}", user_score);

        // 获取这个实例的所有keys
        let user_keys = user_mmkv.all_keys();
        println!("\nuser_data MMKV 中的所有keys:");
        for (index, key) in user_keys.iter().enumerate() {
            println!("  {}: {}", index + 1, key);
        }
        println!("user_data 总keys数: {}", user_keys.len());
    } else {
        println!("创建 user_data MMKV 实例失败");
    }

    // 创建另一个带加密的 MMKV 实例
    println!("\n=== 创建加密的 MMKV 实例 ===");
    let encrypted_mmkv = Mmkv::with_id(
        "encrypted_data",      // mmapID
        0,                     // size
        MMKV_SINGLE_PROCESS,   // mode
        Some("my_secret_key"), // cryptKey (使用加密)
        None,                  // rootPath
        0                      // expectedCapacity
    );

    if let Some(encrypted_mmkv) = encrypted_mmkv {
        println!("成功创建加密的 encrypted_data MMKV 实例");

        // 存储敏感数据
        encrypted_mmkv.set_i32("secret_code", 666888);
        encrypted_mmkv.set_i32("private_key", 123456);

        let secret = encrypted_mmkv.get_i32("secret_code", 0);
        let private_key = encrypted_mmkv.get_i32("private_key", 0);

        println!("秘密代码: {}", secret);
        println!("私钥: {}", private_key);

        let encrypted_keys = encrypted_mmkv.all_keys();
        println!("加密实例中的keys数量: {}", encrypted_keys.len());
    } else {
        println!("创建加密 MMKV 实例失败");
    }
}