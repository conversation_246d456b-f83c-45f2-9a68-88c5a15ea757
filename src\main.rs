// src/main.rs
use mmkvreader::Mmkv;
use std::path::Path;

fn main() {
    // 初始化
    Mmkv::initialize(Path::new("E:\\360MoveData\\Users\\LiShixi\\Desktop\\CK20240714\\mmkv")).unwrap();
    
    // 使用默认 MMKV 实例
    let mmkv = Mmkv::default();

    // 存储一些测试数据
    mmkv.set_i32("test_key1", 42);
    mmkv.set_i32("test_key2", 100);
    mmkv.set_i32("number", 999);

    // 读取数据
    let value1 = mmkv.get_i32("test_key1", 0);
    let value2 = mmkv.get_i32("test_key2", 0);
    let value3 = mmkv.get_i32("number", 0);

    println!("test_key1 value: {}", value1);
    println!("test_key2 value: {}", value2);
    println!("number value: {}", value3);

    // 获取所有keys并输出
    let all_keys = mmkv.all_keys();
    println!("\nAll keys in MMKV:");
    for (index, key) in all_keys.iter().enumerate() {
        println!("  {}: {}", index + 1, key);
    }

    if all_keys.is_empty() {
        println!("  No keys found in MMKV");
    } else {
        println!("\nTotal keys: {}", all_keys.len());
    }
}