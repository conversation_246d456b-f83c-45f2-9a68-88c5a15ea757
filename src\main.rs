// src/main.rs
use mmkvreader::Mmkv;
use std::path::Path;
use clap::{Parser, ValueEnum};

#[derive(Parser)]
#[command(name = "mmkvreader")]
#[command(about = "MMKV 批量搜索工具")]
#[command(version = "1.0")]
struct Args {
    /// tgz 文件所在目录
    #[arg(value_name = "DIRECTORY")]
    directory: String,

    /// 加密密钥 (可选)
    #[arg(short = 'k', long = "key", default_value = "fd9f5bef68c54a1ecf70757a6d6f565b")]
    crypt_key: String,

    /// 搜索关键字 (支持多个关键字，分隔符: 中文逗号、英文逗号、空格、竖杠)
    #[arg(short = 's', long = "search")]
    search_terms: String,

    /// 搜索模式
    #[arg(short = 'm', long = "mode", default_value = "key")]
    search_mode: SearchModeArg,

    /// 是否压缩结果到 zip 文件
    #[arg(short = 'z', long = "zip", default_value = "yes")]
    compress: CompressArg,
}

#[derive(Clone, ValueEnum)]
enum SearchModeArg {
    /// 在 key 中搜索
    Key,
    /// 在 value 中搜索
    Value,
}

#[derive(Clone, ValueEnum)]
enum CompressArg {
    /// 压缩结果
    Yes,
    /// 不压缩结果
    No,
}

fn main() {
    let args = Args::parse();

    // 解析搜索关键字
    let search_keywords = parse_search_terms(&args.search_terms);

    // 转换搜索模式
    let search_mode = match args.search_mode {
        SearchModeArg::Key => 1,
        SearchModeArg::Value => 2,
    };

    // 转换压缩选项
    let should_compress = matches!(args.compress, CompressArg::Yes);

    println!("🔍 MMKV 批量搜索工具");
    println!("{}", "=".repeat(50));
    println!("📁 目录: {}", args.directory);
    println!("🔑 加密密钥: {}", args.crypt_key);
    println!("🔍 搜索关键字: {:?}", search_keywords);
    println!("📋 搜索模式: {:?}", args.search_mode);
    println!("📦 压缩结果: {}", if should_compress { "是" } else { "否" });
    println!();

    // 检查目录是否存在
    if !Path::new(&args.directory).exists() {
        eprintln!("❌ 错误: 目录不存在: {}", args.directory);
        std::process::exit(1);
    }

    // 执行搜索
    run_batch_search(&args.directory, &args.crypt_key, &search_keywords, search_mode, should_compress);
}

/// 解析搜索关键字，支持多种分隔符
fn parse_search_terms(input: &str) -> Vec<String> {
    // 支持的分隔符：中文逗号、英文逗号、空格、竖杠
    let separators = ['，', ',', ' ', '|'];

    let mut terms = Vec::new();
    let mut current_term = String::new();

    for ch in input.chars() {
        if separators.contains(&ch) {
            if !current_term.trim().is_empty() {
                terms.push(current_term.trim().to_string());
                current_term.clear();
            }
        } else {
            current_term.push(ch);
        }
    }

    // 添加最后一个关键字
    if !current_term.trim().is_empty() {
        terms.push(current_term.trim().to_string());
    }

    // 去重并过滤空字符串
    let mut unique_terms: Vec<String> = terms.into_iter()
        .filter(|s| !s.is_empty())
        .collect::<std::collections::HashSet<_>>()
        .into_iter()
        .collect();

    unique_terms.sort();
    unique_terms
}

/// 执行批量搜索
fn run_batch_search(
    directory: &str,
    crypt_key: &str,
    search_keywords: &[String],
    search_mode: i32,
    should_compress: bool,
) {
    let start_time = std::time::Instant::now();
    let mut all_results = Vec::new();
    let mut total_matches = 0;

    // 为每个关键字执行搜索
    for (index, keyword) in search_keywords.iter().enumerate() {
        println!("🔍 搜索关键字 {}/{}: \"{}\"", index + 1, search_keywords.len(), keyword);

        match Mmkv::search_in_tgz_directory(
            directory,
            Some(crypt_key),
            keyword,
            search_mode,
        ) {
            Ok(stats) => {
                println!("✅ 关键字 \"{}\" 搜索完成: 处理 {} 文件，找到 {} 匹配",
                         keyword, stats.processed_files, stats.total_matches);
                total_matches += stats.total_matches;

                // 收集这个关键字的结果文件
                if let Ok(result_files) = collect_result_files(directory, keyword) {
                    all_results.extend(result_files);
                }
            }
            Err(e) => {
                eprintln!("❌ 关键字 \"{}\" 搜索失败: {}", keyword, e);
            }
        }

        if index < search_keywords.len() - 1 {
            println!();
        }
    }

    let total_time = start_time.elapsed();

    println!("\n{}", "=".repeat(50));
    println!("🎉 所有搜索任务完成!");
    println!("📊 总体统计:");
    println!("   搜索关键字: {} 个", search_keywords.len());
    println!("   总匹配数: {}", total_matches);
    println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());

    // 处理结果文件
    if should_compress && !all_results.is_empty() {
        match create_zip_archive(directory, &all_results, search_keywords) {
            Ok(zip_path) => {
                println!("📦 结果已压缩到: {}", zip_path);

                // 删除原始 JSON 文件
                for file_path in &all_results {
                    let _ = std::fs::remove_file(file_path);
                }
                println!("🧹 已清理 {} 个临时 JSON 文件", all_results.len());
            }
            Err(e) => {
                eprintln!("❌ 创建 ZIP 文件失败: {}", e);
            }
        }
    } else if !all_results.is_empty() {
        println!("📄 生成了 {} 个 JSON 结果文件", all_results.len());
    }
}

/// 收集指定关键字的结果文件
fn collect_result_files(directory: &str, keyword: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let mut result_files = Vec::new();

    for entry in std::fs::read_dir(directory)? {
        let entry = entry?;
        let path = entry.path();

        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            if file_name.ends_with("_search_results.json") {
                // 检查文件内容是否包含当前关键字的搜索结果
                if let Ok(content) = std::fs::read_to_string(&path) {
                    if content.contains(keyword) {
                        result_files.push(path.to_string_lossy().to_string());
                    }
                }
            }
        }
    }

    Ok(result_files)
}

/// 创建 ZIP 压缩包
fn create_zip_archive(
    directory: &str,
    result_files: &[String],
    search_keywords: &[String],
) -> Result<String, Box<dyn std::error::Error>> {
    use std::io::Write;

    // 生成 ZIP 文件名
    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
    let keywords_str = search_keywords.join("_");
    let safe_keywords = keywords_str.chars()
        .map(|c| if c.is_alphanumeric() || c == '_' { c } else { '_' })
        .collect::<String>();

    let zip_filename = format!("mmkv_search_{}_{}.zip", safe_keywords, timestamp);
    let zip_path = Path::new(directory).join(&zip_filename);

    // 创建 ZIP 文件
    let zip_file = std::fs::File::create(&zip_path)?;
    let mut zip = zip::ZipWriter::new(zip_file);

    let options = zip::write::FileOptions::default()
        .compression_method(zip::CompressionMethod::Deflated)
        .unix_permissions(0o755);

    // 添加每个结果文件到 ZIP
    for file_path in result_files {
        let file_name = Path::new(file_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown.json");

        zip.start_file(file_name, options)?;

        let content = std::fs::read(file_path)?;
        zip.write_all(&content)?;
    }

    // 创建搜索摘要文件
    let summary = create_search_summary(search_keywords, result_files.len());
    zip.start_file("search_summary.txt", options)?;
    zip.write_all(summary.as_bytes())?;

    zip.finish()?;

    Ok(zip_path.to_string_lossy().to_string())
}

/// 创建搜索摘要
fn create_search_summary(search_keywords: &[String], file_count: usize) -> String {
    let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC");

    format!(
        "MMKV 搜索结果摘要\n\
        ==================\n\
        \n\
        搜索时间: {}\n\
        搜索关键字: {}\n\
        结果文件数: {}\n\
        \n\
        关键字详情:\n\
        {}\n\
        \n\
        说明:\n\
        - 每个 JSON 文件包含对应 tgz 文件的搜索结果\n\
        - 文件名格式: <tgz文件名>_search_results.json\n\
        - 搜索结果包含匹配的 key/value 及其详细信息\n",
        timestamp,
        search_keywords.join(", "),
        file_count,
        search_keywords.iter()
            .enumerate()
            .map(|(i, keyword)| format!("  {}. {}", i + 1, keyword))
            .collect::<Vec<_>>()
            .join("\n")
    )
}

