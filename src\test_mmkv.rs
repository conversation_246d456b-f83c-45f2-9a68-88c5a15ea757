use crate::Mmkv;

pub fn test_mmkv_with_params() {
    println!("🧪 开始MMKV测试");
    println!("{}", "=".repeat(50));

    let crypt_key = "fd9f5bef68c54a1ecf70757a6d6f565b";
    let mmkv_id = "azeroth";
    let root_path = r"E:\360MoveData\Users\LiShixi\Desktop\CK20240714\<EMAIL>";
    
    println!("📋 测试参数:");
    println!("   密钥: {}", crypt_key);
    println!("   mmapID: {}", mmkv_id);
    println!("   rootPath: {}", root_path);
    println!();
    
    // 检查路径是否存在
    let path = std::path::Path::new(root_path);
    if !path.exists() {
        println!("❌ 错误: 路径不存在: {}", root_path);
        return;
    }
    
    // 检查azeroth文件是否存在
    let azeroth_file = path.join("azeroth");
    if !azeroth_file.exists() {
        println!("❌ 错误: azeroth文件不存在: {}", azeroth_file.display());
        return;
    }
    
    println!("✅ 路径和文件都存在");
    println!("📁 azeroth文件路径: {}", azeroth_file.display());
    
    // 获取文件大小
    if let Ok(metadata) = std::fs::metadata(&azeroth_file) {
        println!("📊 文件大小: {} 字节", metadata.len());
    }
    
    println!();
    println!("🔧 初始化MMKV库...");

    // 初始化MMKV库
    match Mmkv::initialize(path) {
        Ok(_) => {
            println!("✅ MMKV库初始化成功");
        }
        Err(code) => {
            println!("⚠️  MMKV库初始化失败，错误代码: {}", code);
            println!("   继续尝试加载...");
        }
    }

    println!("🔄 尝试加载MMKV...");

    // 尝试加载MMKV
    match Mmkv::with_id(
        mmkv_id,
        0,
        crate::MMKV_SINGLE_PROCESS,
        Some(crypt_key),
        Some(root_path),
        0,
    ) {
        Some(mmkv) => {
            println!("✅ MMKV加载成功!");
            
            // 获取所有keys
            println!("\n🔑 获取所有keys...");
            let all_keys = mmkv.all_keys();
            println!("📊 总key数量: {}", all_keys.len());
            
            if all_keys.is_empty() {
                println!("⚠️  没有找到任何key");
            } else {
                println!("\n📋 所有keys列表:");
                for (i, key) in all_keys.iter().enumerate() {
                    println!("  {}: {}", i + 1, key);
                }
                
                println!("\n🔍 遍历所有key的值:");
                println!("{}", "-".repeat(50));
                
                for key in &all_keys {
                    let value = mmkv.get_value(key);
                    match value {
                        crate::MmkvValue::String(s) => {
                            // 检查是否包含 ksvodplayer
                            if s.to_lowercase().contains("ksvodplayer") {
                                println!("🎯 找到匹配! [{}] 包含 'ksvodplayer'", key);
                                println!("🔤 [{}] = \"{}\"", key, &s[..std::cmp::min(200, s.len())]);
                                if s.len() > 200 {
                                    println!("   ... (内容被截断，总长度: {} 字符)", s.len());
                                }
                            } else {
                                println!("🔤 [{}] = \"{}\"", key, &s[..std::cmp::min(100, s.len())]);
                                if s.len() > 100 {
                                    println!("   ... (内容被截断，总长度: {} 字符)", s.len());
                                }
                            }
                        }
                        crate::MmkvValue::Integer(i) => {
                            println!("🔢 [{}] = {}", key, i);
                        }
                        crate::MmkvValue::Unknown => {
                            println!("❓ [{}] = <unknown type>", key);
                        }
                    }
                }
            }
        }
        None => {
            println!("❌ MMKV加载失败!");
            
            // 尝试不同的加载方式
            println!("\n🔄 尝试其他加载方式...");
            
            // 尝试不使用加密
            println!("🔓 尝试不使用加密...");
            match Mmkv::with_id(
                mmkv_id,
                0,
                crate::MMKV_SINGLE_PROCESS,
                None,
                Some(root_path),
                0,
            ) {
                Some(mmkv) => {
                    println!("✅ 无加密模式加载成功!");
                    let all_keys = mmkv.all_keys();
                    println!("📊 总key数量: {}", all_keys.len());
                    
                    if !all_keys.is_empty() {
                        println!("📋 前10个keys:");
                        for (i, key) in all_keys.iter().take(10).enumerate() {
                            println!("  {}: {}", i + 1, key);
                        }
                    }
                }
                None => {
                    println!("❌ 无加密模式也加载失败");
                }
            }
            
            // 尝试使用文件名作为mmapID
            println!("\n📁 尝试使用完整文件路径...");
            match Mmkv::with_id(
                &azeroth_file.to_string_lossy(),
                0,
                crate::MMKV_SINGLE_PROCESS,
                Some(crypt_key),
                None,
                0,
            ) {
                Some(mmkv) => {
                    println!("✅ 使用完整路径加载成功!");
                    let all_keys = mmkv.all_keys();
                    println!("📊 总key数量: {}", all_keys.len());
                }
                None => {
                    println!("❌ 使用完整路径也加载失败");
                }
            }
        }
    }
    
    println!("\n🏁 测试完成");
}
